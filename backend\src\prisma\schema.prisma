

datasource db {
  provider = "postgresql" 
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model Admin {
  id    Int     @id @default(autoincrement())
  email String  @unique
  name  String
  password  String
}

model Enclosure {
  id        Int      @id @default(autoincrement())
  name      String              // e.g Big Cat Area
  type      String              // e.g "Savannah", "Aquarium", "Bird Cage"
  capacity  Int                 // e.g  2 , 8 , animals
  condition String   @default("Good") // e.g "Good", "Needs Maintenance"
  location  String?
  animals   Animal[]            // one-to-many relation

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Animal {
  id            Int      @id @default(autoincrement())
  name          String
  species       String
  age           Int
  gender        String
  health_status String   @default("Healthy")
  arrival_date  DateTime @default(now())

  // relation to enclosure
  enclosureId   Int            // many-to-one relation
  enclosure     Enclosure @relation(fields: [enclosureId], references: [id])

  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}


